<?php

namespace Database\Seeders;

use App\Models\Constant;
use App\Models\ConstantGroup;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        ConstantGroup::create([
            'name' => 'order_status',
        ]);

        Constant::create([
            'constant_group_id' => 1,
            'name' => 'new_order',
            'order' => 0,
            'value' => 'New Order',
        ]);
        Constant::create([
            'constant_group_id' => 1,
            'name' => 'job_accepted',
            'order' => 10,
            'value' => 'Job accepted',
        ]);
        Constant::create([
            'constant_group_id' => 1,
            'name' => 'arrived_at_address',
            'order' => 20,
            'value' => 'Arrived at address',
        ]);
        Constant::create([
            'constant_group_id' => 1,
            'name' => 'car_picked_up',
            'order' => 30,
            'value' => 'Picked up car',
        ]);
        Constant::create([
            'constant_group_id' => 1,
            'name' => 'ride_started',
            'order' => 40,
            'value' => 'Ride started',
        ]);
        Constant::create([
            'constant_group_id' => 1,
            'name' => 'arrived_at_destination',
            'order' => 50,
            'value' => 'Arrived at destination',
        ]);
        Constant::create([
            'constant_group_id' => 1,
            'name' => 'payed_ride_ended',
            'order' => 60,
            'value' => 'Payed - Ride ended',
        ]);
        Constant::create([
            'constant_group_id' => 1,
            'name' => 'canceled',
            'order' => 1000,
            'value' => 'Canceled',
        ]);
    }
}
