<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('address_from');
            $table->string('address_to');
            $table->string('address_passby')->nullable();
            $table->dateTime('pickup_time');
            $table->integer('offer_price')->nullable();
            $table->string('note')->default('');
            $table->foreignId('client_id')->constrained('clients');
            $table->foreignId('car_id')->constrained('cars');
            $table->foreignId('status_id')->constrained('constants');
            $table->foreignId('driver_id')->constrained('drivers')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
