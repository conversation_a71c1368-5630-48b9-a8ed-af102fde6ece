<?php

namespace App\Filament\Clusters\Settings\Resources\ConstantGroups\Pages;

use App\Filament\Clusters\Settings\Resources\ConstantGroups\ConstantGroupResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewConstantGroup extends ViewRecord
{
    protected static string $resource = ConstantGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
