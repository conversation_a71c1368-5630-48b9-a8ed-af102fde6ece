<?php

namespace App\Filament\Clusters\Settings\Resources\ConstantGroups\Pages;

use App\Filament\Clusters\Settings\Resources\ConstantGroups\ConstantGroupResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditConstantGroup extends EditRecord
{
    protected static string $resource = ConstantGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
