<?php

namespace App\Filament\Clusters\Settings\Resources\ConstantGroups;

use App\Filament\Clusters\Settings\Resources\ConstantGroups\Pages\CreateConstantGroup;
use App\Filament\Clusters\Settings\Resources\ConstantGroups\Pages\EditConstantGroup;
use App\Filament\Clusters\Settings\Resources\ConstantGroups\Pages\ListConstantGroups;
use App\Filament\Clusters\Settings\Resources\ConstantGroups\Pages\ViewConstantGroup;
use App\Filament\Clusters\Settings\Resources\ConstantGroups\Schemas\ConstantGroupForm;
use App\Filament\Clusters\Settings\Resources\ConstantGroups\Schemas\ConstantGroupInfolist;
use App\Filament\Clusters\Settings\Resources\ConstantGroups\Tables\ConstantGroupsTable;
use App\Filament\Clusters\Settings\SettingsCluster;
use App\Models\ConstantGroup;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class ConstantGroupResource extends Resource
{
    protected static ?string $model = ConstantGroup::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $cluster = SettingsCluster::class;

    public static function form(Schema $schema): Schema
    {
        return ConstantGroupForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return ConstantGroupInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ConstantGroupsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListConstantGroups::route('/'),
            'create' => CreateConstantGroup::route('/create'),
            'view' => ViewConstantGroup::route('/{record}'),
            'edit' => EditConstantGroup::route('/{record}/edit'),
        ];
    }
}
