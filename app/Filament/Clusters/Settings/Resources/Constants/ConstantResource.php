<?php

namespace App\Filament\Clusters\Settings\Resources\Constants;

use App\Filament\Clusters\Settings\Resources\Constants\Pages\CreateConstant;
use App\Filament\Clusters\Settings\Resources\Constants\Pages\EditConstant;
use App\Filament\Clusters\Settings\Resources\Constants\Pages\ListConstants;
use App\Filament\Clusters\Settings\Resources\Constants\Pages\ViewConstant;
use App\Filament\Clusters\Settings\Resources\Constants\Schemas\ConstantForm;
use App\Filament\Clusters\Settings\Resources\Constants\Schemas\ConstantInfolist;
use App\Filament\Clusters\Settings\Resources\Constants\Tables\ConstantsTable;
use App\Filament\Clusters\Settings\SettingsCluster;
use App\Models\Constant;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class ConstantResource extends Resource
{
    protected static ?string $model = Constant::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $cluster = SettingsCluster::class;

    public static function form(Schema $schema): Schema
    {
        return ConstantForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return ConstantInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ConstantsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListConstants::route('/'),
            'create' => CreateConstant::route('/create'),
            'view' => ViewConstant::route('/{record}'),
            'edit' => EditConstant::route('/{record}/edit'),
        ];
    }
}
