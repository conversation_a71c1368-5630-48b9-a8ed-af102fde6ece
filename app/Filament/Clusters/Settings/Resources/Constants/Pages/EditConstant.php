<?php

namespace App\Filament\Clusters\Settings\Resources\Constants\Pages;

use App\Filament\Clusters\Settings\Resources\Constants\ConstantResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditConstant extends EditRecord
{
    protected static string $resource = ConstantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
