<?php

namespace App\Filament\Clusters\Settings\Resources\Constants\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class ConstantForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                TextInput::make('value')
                    ->required(),
                TextInput::make('order')
                    ->required()
                    ->numeric(),
                TextInput::make('constant_group_id')
                    ->required()
                    ->numeric(),
            ]);
    }
}
