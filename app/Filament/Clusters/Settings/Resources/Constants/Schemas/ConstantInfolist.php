<?php

namespace App\Filament\Clusters\Settings\Resources\Constants\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class ConstantInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('name'),
                TextEntry::make('value'),
                TextEntry::make('order')
                    ->numeric(),
                TextEntry::make('constantGroup.name')->label('Group')
                    ->numeric(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
            ]);
    }
}
