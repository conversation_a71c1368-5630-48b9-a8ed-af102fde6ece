<?php

namespace App\Filament\Resources\Orders\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class OrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('address_from')
                    ->searchable(),
                TextColumn::make('address_to')
                    ->searchable(),
                TextColumn::make('address_passby')
                    ->searchable(),
                TextColumn::make('pickup_time')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('offer_price')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('note')
                    ->searchable(),
                TextColumn::make('client.id')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('car.id')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('status.name')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('driver.id')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
