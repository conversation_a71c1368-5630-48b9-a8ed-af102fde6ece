<?php

namespace App\Filament\Resources\Orders\Schemas;

use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class OrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('address_from')
                    ->required(),
                TextInput::make('address_to')
                    ->required(),
                TextInput::make('address_passby'),
                DateTimePicker::make('pickup_time')
                    ->required(),
                TextInput::make('offer_price')
                    ->numeric(),
                TextInput::make('note')
                    ->required(),
                Select::make('client_id')
                    ->relationship('client', 'id')
                    ->required(),
                Select::make('car_id')
                    ->relationship('car', 'id')
                    ->required(),
                Select::make('status_id')
                    ->relationship('status', 'name')
                    ->required(),
                Select::make('driver_id')
                    ->relationship('driver', 'id')
                    ->required(),
            ]);
    }
}
