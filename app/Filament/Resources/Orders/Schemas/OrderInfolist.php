<?php

namespace App\Filament\Resources\Orders\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class OrderInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('address_from'),
                TextEntry::make('address_to'),
                TextEntry::make('address_passby'),
                TextEntry::make('pickup_time')
                    ->dateTime(),
                TextEntry::make('offer_price')
                    ->numeric(),
                TextEntry::make('note'),
                TextEntry::make('client.id')
                    ->numeric(),
                TextEntry::make('car.id')
                    ->numeric(),
                TextEntry::make('status.name')
                    ->numeric(),
                TextEntry::make('driver.id')
                    ->numeric(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
            ]);
    }
}
