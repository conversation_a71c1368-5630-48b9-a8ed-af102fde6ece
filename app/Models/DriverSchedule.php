<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\DriverSchedule
 *
 * @property-read int $id
 * @property-read int $driver_id
 * @property-read int $day_of_week
 * @property-read string $start_time
 * @property-read string $end_time
 * @property-read \Illuminate\Support\Carbon|null $created_at
 * @property-read \Illuminate\Support\Carbon|null $updated_at
 */
class DriverSchedule extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'driver_id',
        'day_of_week',
        'start_time',
        'end_time',
    ];
}
