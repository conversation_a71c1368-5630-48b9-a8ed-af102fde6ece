<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Driver
 *
 * @property-read int $id
 * @property-read string $name
 * @property-read string $value
 * @property-read int $order
 * @property-read \Illuminate\Support\Carbon|null $created_at
 * @property-read \Illuminate\Support\Carbon|null $updated_at
 */
class Constant extends Model
{
    /**
     *
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'value'
    ];

    /**
     * Get the group that owns the constant.
     *
     * @return BelongsTo
     */
    public function constantGroup(): BelongsTo
    {
        return $this->belongsTo(ConstantGroup::class);
    }

    public function getConstantGroupAttribute()
    {
        return $this->constantGroup()->first();
    }
}
