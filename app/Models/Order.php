<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\DriverSchedule
 *
 * @property-read int $id
 * @property string $address_from
 * @property string $address_to
 * @property array $address_passby
 * @property string $pickup_time
 * @property float $offer_price
 * @property string $note
 * @property int $client_id
 * @property int $car_id
 * @property int $status_id
 * @property int $driver_id
 * @property-read \Illuminate\Support\Carbon|null $created_at
 * @property-read \Illuminate\Support\Carbon|null $updated_at
 */
class Order extends Model
{
    protected $fillable = [
        'address_from',
        'address_to',
        'address_passby',
        'pickup_time',
        'offer_price',
        'note',
        'client_id',
        'car_id',
        'status_id',
        'driver_id',
    ];

    protected $casts = [
        'address_passby' => 'array',
    ];

    /**
     * Get the client that owns the order.
     *
     * @return BelongsTo
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the car that is requested in the order.
     *
     * @return BelongsTo
     */
    public function car(): BelongsTo
    {
        return $this->belongsTo(Car::class);
    }

    /**
     * Get the current status of the order.
     *
     * @return BelongsTo
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(Constant::class);
    }

    /**
     * Get the driver that is assigned to the order.
     *
     * @return BelongsTo
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }
}
