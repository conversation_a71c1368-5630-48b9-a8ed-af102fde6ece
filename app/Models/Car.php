<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Car
 *
 * @property-read int $id
 * @property-read string $brand
 * @property-read string $model
 * @property-read string $color
 * @property-read string $license_plate
 * @property-read \Illuminate\Support\Carbon|null $created_at
 * @property-read \Illuminate\Support\Carbon|null $updated_at
 */
class Car extends Model
{
    protected $fillable = [
        'brand',
        'model',
        'color',
        'license_plate',
    ];
}
