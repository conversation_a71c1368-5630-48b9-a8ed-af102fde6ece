<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Driver
 *
 * @property-read int $id
 * @property-read string $name
 * @property-read \Illuminate\Support\Carbon|null $created_at
 * @property-read \Illuminate\Support\Carbon|null $updated_at
 */
class ConstantGroup extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name'];

    /**
     * Get the constants for the constant group.
     *
     * @return HasMany
     */
    public function constants(): HasMany
    {
        return $this->hasMany(Constant::class);
    }
}
